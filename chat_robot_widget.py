import sys
import json
import os
import subprocess
from pathlib import Path
from PyQt5.QtWidgets import (QApplication, QMainWindow, QVBoxLayout, QHBoxLayout,
                               QWidget, QPushButton, QLabel, QTextEdit, QComboBox,
                               QProgressBar, QScrollArea, QFrame, QMessageBox, QListWidget,
                               QSplitter, QGroupBox, QGridLayout, QLineEdit, QListWidgetItem)
from PyQt5.QtCore import Qt, QTimer, QThread, pyqtSignal as Signal, QRect, QPoint
from PyQt5.QtGui import QPixmap, QFont, QPalette, QColor, QPainter, QPen, QBrush
import pyttsx3
import threading

class VoiceThread(QThread):
    """语音播放线程"""
    def __init__(self, text):
        super().__init__()
        self.text = text

    def run(self):
        try:
            engine = pyttsx3.init()
            engine.setProperty('rate', 220)  # 语速
            engine.setProperty('volume', 1)  # 音量
            engine.say(self.text)
            engine.runAndWait()
        except Exception as e:
            print(f"语音播放错误: {e}")

class FlowchartWidget(QWidget):
    """横向流程图显示组件"""
    voice_clicked = Signal(int)  # 添加语音点击信号

    def __init__(self):
        super().__init__()
        self.current_step = 0
        self.steps = []
        self.setMinimumHeight(200)
        self.setMaximumHeight(200)
        self.speaker_rects = []  # 存储喇叭图标的位置
        self.show_welcome = True  # 添加欢迎界面标志

    def update_flowchart(self, steps):
        """更新流程图"""
        self.steps = steps
        self.show_welcome = False  # 有流程时隐藏欢迎界面
        self.show_welcome = False  # 有流程时隐藏欢迎界面
        self.update()

    def highlight_current_step(self, step_index):
        """高亮当前步骤"""
        self.current_step = step_index
        self.update()

    def paintEvent(self, event):
        """绘制流程图"""
        painter = QPainter(self)
        painter.setRenderHint(QPainter.Antialiasing)
        
        if not self.steps and self.show_welcome:
            # 显示使用提示
            painter.setFont(QFont("Microsoft YaHei", 15))
            painter.setPen(QPen(QColor(120, 120, 120)))
            tip_text = "请在下方输入你想要的调试与测试操作流程名称即可生成操作流程"
            painter.drawText(self.rect(), Qt.AlignCenter, tip_text)
            return
        
        if not self.steps:
            return

        # 清空喇叭图标位置记录
        self.speaker_rects = []

        # 计算每个步骤框的位置和大小
        widget_width = self.width()
        widget_height = self.height()

        step_count = len(self.steps)
        if step_count == 0:
            return

        # 计算步骤框的尺寸和间距
        margin = 20
        arrow_width = 30
        available_width = widget_width - 2 * margin - (step_count - 1) * arrow_width
        step_width = available_width // step_count
        step_height = 80
        step_y = (widget_height - step_height) // 2

        # 绘制每个步骤
        for i, step in enumerate(self.steps):
            step_x = margin + i * (step_width + arrow_width)

            # 设置颜色 - 简洁的配色方案
            if i == self.current_step:
                # 当前步骤 - 蓝色高亮
                brush = QBrush(QColor(33, 150, 243))   # 蓝色
                pen = QPen(QColor(25, 118, 210), 2)    # 深蓝色边框
                text_color = QColor(255, 255, 255)     # 白色文字
            elif i < self.current_step:
                # 已完成步骤 - 浅灰色
                brush = QBrush(QColor(245, 245, 245))  # 浅灰色
                pen = QPen(QColor(224, 224, 224), 1)   # 灰色边框
                text_color = QColor(158, 158, 158)     # 灰色文字
            else:
                # 未开始步骤 - 白色
                brush = QBrush(QColor(255, 255, 255))  # 白色
                pen = QPen(QColor(224, 224, 224), 1)   # 浅灰色边框
                text_color = QColor(97, 97, 97)        # 深灰色文字

            # 绘制矩形框
            painter.setBrush(brush)
            painter.setPen(pen)
            rect = QRect(step_x, step_y, step_width, step_height)
            painter.drawRoundedRect(rect, 8, 8)

            # 绘制步骤文字
            painter.setPen(QPen(text_color))
            painter.setFont(QFont("Microsoft YaHei", 11, QFont.Bold))

            # 步骤编号
            number_text = f"{i + 1}"
            painter.drawText(step_x + 8, step_y + 18, number_text)

            # 步骤标题（截断过长的文字）
            title = step['title']
            if len(title) > 6:
                title = title[:6] + "..."

            painter.setFont(QFont("Microsoft YaHei", 13))
            painter.drawText(step_x + 8, step_y + 35, step_width - 16, 40,
                           Qt.AlignLeft | Qt.TextWordWrap, title)

            # 绘制箭头（除了最后一个步骤）
            if i < step_count - 1:
                arrow_x = step_x + step_width + 8
                arrow_y = step_y + step_height // 2

                painter.setPen(QPen(QColor(189, 189, 189), 1))
                # 箭头线
                painter.drawLine(arrow_x, arrow_y, arrow_x + arrow_width - 16, arrow_y)
                # 箭头头部
                painter.drawLine(arrow_x + arrow_width - 16, arrow_y,
                               arrow_x + arrow_width - 20, arrow_y - 4)
                painter.drawLine(arrow_x + arrow_width - 16, arrow_y,
                               arrow_x + arrow_width - 20, arrow_y + 4)

            # 绘制喇叭图标
            speaker_size = 18
            speaker_x = step_x + step_width - speaker_size - 8
            speaker_y = step_y + 8
            speaker_rect = QRect(speaker_x, speaker_y, speaker_size, speaker_size)

            # 保存喇叭图标位置用于点击检测
            self.speaker_rects.append((speaker_rect, i))

            # 绘制喇叭图标背景 - 简洁的圆形背景
            painter.setBrush(QBrush(QColor(97, 97, 97, 180)))  # 深灰色半透明背景
            painter.setPen(QPen(QColor(97, 97, 97), 1))
            painter.drawEllipse(speaker_rect)

            # 绘制喇叭符号
            painter.setPen(QPen(QColor(255, 255, 255)))
            painter.setFont(QFont("Arial", 11))
            painter.drawText(speaker_rect, Qt.AlignCenter, "♪")

    def mousePressEvent(self, event):
        """处理鼠标点击事件"""
        if event.button() == Qt.LeftButton:
            click_pos = event.pos()
            # 检查是否点击了喇叭图标
            for speaker_rect, step_index in self.speaker_rects:
                if speaker_rect.contains(click_pos):
                    self.voice_clicked.emit(step_index)
                    break

class ProcessFlowAssistant(QMainWindow):
    def __init__(self):
        super().__init__()
        self.current_process = None
        self.current_step_index = 0
        self.process_data = {}

        self.init_ui()
        self.load_process_data()
        self.show_welcome_content()  # 添加欢迎内容显示

    def init_ui(self):
        self.setWindowTitle("流程生成助手")
        self.setGeometry(0, 0, 1900, 1000)
        self.setFixedSize(1900, 1000)

        # 创建中央组件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)

        # 主布局 - 垂直布局
        main_layout = QVBoxLayout(central_widget)
        main_layout.setSpacing(10)
        main_layout.setContentsMargins(20, 20, 20, 20)

        # 顶部标题区域
        title_label = QLabel("流程生成助手")
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setFont(QFont("Microsoft YaHei", 20, QFont.Bold))
        title_label.setStyleSheet("""
            QLabel {
                color: #333333;
                padding: 15px 0px;
                background-color: transparent;
            }
        """)
        main_layout.addWidget(title_label)

        # 流程图区域
        self.flowchart = FlowchartWidget()
        # 连接喇叭点击信号
        self.flowchart.voice_clicked.connect(self.play_voice_for_step)
        main_layout.addWidget(self.flowchart)

        # 中间内容区域 - 水平布局
        content_layout = QHBoxLayout()
        content_layout.setSpacing(15)

        # 技能要点区域
        skills_panel = self.create_skills_panel()
        content_layout.addWidget(skills_panel)

        # 所需工具区域
        tools_panel = self.create_tools_panel()
        content_layout.addWidget(tools_panel)

        # 注意事项区域
        cautions_panel = self.create_cautions_panel()
        content_layout.addWidget(cautions_panel)

        main_layout.addLayout(content_layout)

        # 控制按钮区域
        control_panel = self.create_control_panel()
        main_layout.addWidget(control_panel)

        # 底部流程选择区域
        process_panel = self.create_process_selection_panel()
        main_layout.addWidget(process_panel)

        # 设置布局比例
        main_layout.setStretchFactor(title_label, 0)  # 标题固定高度
        main_layout.setStretchFactor(self.flowchart, 3)  # 流程图增加空间
        main_layout.setStretchFactor(content_layout, 2)  # 三栏内容减少空间
        main_layout.setStretchFactor(control_panel, 1)
        main_layout.setStretchFactor(process_panel, 1)

    def create_skills_panel(self):
        """创建技能要点面板"""
        group = QGroupBox("技能要点")
        group.setStyleSheet("""
            QGroupBox {
                font-weight: 500;
                font-size: 17px;
                border: 1px solid #e0e0e0;
                border-radius: 12px;
                margin-top: 12px;
                padding-top: 16px;
                background-color: #ffffff;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 16px;
                padding: 0 8px 0 8px;
                color: #333333;
                background-color: #ffffff;
            }
        """)

        layout = QVBoxLayout(group)

        self.skills_text = QTextEdit()
        self.skills_text.setReadOnly(True)
        self.skills_text.setMaximumHeight(400)
        self.skills_text.setStyleSheet("""
            QTextEdit {
                border: 1px solid #e0e0e0;
                border-radius: 8px;
                padding: 12px;
                background-color: #fafafa;
                font-size: 15px;
                font-family: "Microsoft YaHei";
                color: #333333;
                line-height: 1.6;
            }
        """)

        layout.addWidget(self.skills_text)
        return group

    def create_tools_panel(self):
        """创建所需工具面板"""
        group = QGroupBox("所需工具")
        group.setStyleSheet("""
            QGroupBox {
                font-weight: 500;
                font-size: 17px;
                border: 1px solid #e0e0e0;
                border-radius: 12px;
                margin-top: 12px;
                padding-top: 16px;
                background-color: #ffffff;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 16px;
                padding: 0 8px 0 8px;
                color: #333333;
                background-color: #ffffff;
            }
        """)

        layout = QVBoxLayout(group)

        # 工具列表
        self.tools_list = QListWidget()
        self.tools_list.setMaximumHeight(400)
        self.tools_list.setStyleSheet("""
            QListWidget {
                border: 1px solid #e0e0e0;
                border-radius: 8px;
                background-color: #fafafa;
                font-size: 15px;
                font-family: "Microsoft YaHei";
                color: #333333;
            }
            QListWidget::item {
                padding: 6px 6px;
                border-bottom: 1px solid #f0f0f0;
                margin-bottom: 4px;
            }
            QListWidget::item:selected {
                background-color: #f5f5f5;
                color: #2196F3;
            }
        """)

        layout.addWidget(self.tools_list)
        return group

    def create_cautions_panel(self):
        """创建注意事项面板"""
        group = QGroupBox("注意事项")
        group.setStyleSheet("""
            QGroupBox {
                font-weight: 500;
                font-size: 17px;
                border: 1px solid #e0e0e0;
                border-radius: 12px;
                margin-top: 12px;
                padding-top: 16px;
                background-color: #ffffff;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 16px;
                padding: 0 8px 0 8px;
                color: #333333;
                background-color: #ffffff;
            }
        """)

        layout = QVBoxLayout(group)

        self.cautions_text = QTextEdit()
        self.cautions_text.setReadOnly(True)
        self.cautions_text.setMaximumHeight(400)
        self.cautions_text.setStyleSheet("""
            QTextEdit {
                border: 1px solid #e0e0e0;
                border-radius: 8px;
                padding: 12px;
                background-color: #fff8e1;
                color: #e65100;
                font-size: 15px;
                font-family: "Microsoft YaHei";
                line-height: 1.6;
            }
        """)

        layout.addWidget(self.cautions_text)
        return group

    def create_control_panel(self):
        """创建控制面板"""
        widget = QWidget()
        layout = QVBoxLayout(widget)

        # 控制按钮区域
        button_layout = QHBoxLayout()

        self.prev_button = QPushButton("上一步")
        self.prev_button.clicked.connect(self.prev_step)
        self.prev_button.setEnabled(False)
        self.prev_button.setStyleSheet("""
            QPushButton {
                background-color: #ffffff;
                color: #666666;
                font-weight: 500;
                padding: 12px 24px;
                border: 1px solid #e0e0e0;
                border-radius: 8px;
                font-size: 15px;
            }
            QPushButton:hover {
                background-color: #f5f5f5;
                border-color: #2196F3;
                color: #2196F3;
            }
            QPushButton:disabled {
                background-color: #fafafa;
                color: #cccccc;
                border-color: #f0f0f0;
            }
        """)

        self.confirm_button = QPushButton("下一步")
        self.confirm_button.clicked.connect(self.confirm_step)
        self.confirm_button.setEnabled(False)
        self.confirm_button.setStyleSheet("""
            QPushButton {
                background-color: #2196F3;
                color: white;
                font-weight: 500;
                padding: 12px 32px;
                border: none;
                border-radius: 8px;
                font-size: 15px;
            }
            QPushButton:hover {
                background-color: #1976D2;
            }
            QPushButton:disabled {
                background-color: #e0e0e0;
                color: #999999;
            }
        """)

        self.link_button = QPushButton("查看文档")
        self.link_button.clicked.connect(self.open_document)
        self.link_button.setEnabled(False)
        self.link_button.setStyleSheet("""
            QPushButton {
                background-color: #ffffff;
                color: #666666;
                font-weight: 500;
                padding: 10px 20px;
                border: 1px solid #e0e0e0;
                border-radius: 8px;
                font-size: 14px;
            }
            QPushButton:hover {
                background-color: #f5f5f5;
                border-color: #2196F3;
                color: #2196F3;
            }
            QPushButton:disabled {
                background-color: #fafafa;
                color: #cccccc;
                border-color: #f0f0f0;
            }
        """)

        button_layout.addWidget(self.prev_button)
        button_layout.addStretch()
        button_layout.addWidget(self.confirm_button)
        button_layout.addWidget(self.link_button)

        # 进度显示
        progress_layout = QHBoxLayout()
        progress_label = QLabel("当前进度:")
        progress_label.setFont(QFont("Arial", 11))

        self.progress_label = QLabel("0/0")
        self.progress_label.setFont(QFont("Arial", 11, QFont.Bold))
        self.progress_label.setStyleSheet("color: #2196F3;")

        progress_layout.addWidget(progress_label)
        progress_layout.addWidget(self.progress_label)
        progress_layout.addStretch()

        layout.addLayout(button_layout)
        layout.addLayout(progress_layout)

        return widget

    def create_process_selection_panel(self):
        """创建流程输入面板"""
        widget = QWidget()
        layout = QHBoxLayout(widget)

        process_label = QLabel("请输入需要生成的流程:")
        process_label.setFont(QFont("Microsoft YaHei", 15, QFont.Medium))
        process_label.setStyleSheet("color: #333333;")

        self.process_input = QLineEdit()
        self.process_input.setMinimumWidth(1400)
        self.process_input.setPlaceholderText("例如：毫米波雷达调试、激光雷达标定、摄像头标定、设备维护等")
        self.process_input.setStyleSheet("""
            QLineEdit {
                padding: 12px 16px;
                border: 1px solid #e0e0e0;
                border-radius: 8px;
                font-size: 15px;
                background-color: #ffffff;
                color: #333333;
            }
            QLineEdit:focus {
                border-color: #2196F3;
                outline: none;
            }
            QLineEdit::placeholder {
                color: #999999;
            }
        """)
        # 添加回车键触发生成
        self.process_input.returnPressed.connect(self.generate_process)

        self.generate_button = QPushButton("点击生成")
        self.generate_button.clicked.connect(self.generate_process)
        self.generate_button.setStyleSheet("""
            QPushButton {
                background-color: #4CAF50;
                color: white;
                font-weight: 500;
                padding: 12px 24px;
                border: none;
                border-radius: 8px;
                font-size: 15px;
            }
            QPushButton:hover {
                background-color: #45a049;
            }
            QPushButton:disabled {
                background-color: #e0e0e0;
                color: #999999;
            }
        """)

        # 添加状态提示标签
        self.status_label = QLabel("")
        self.status_label.setStyleSheet("""
            QLabel {
                color: #666666;
                font-size: 13px;
                padding: 4px 8px;
            }
        """)

        layout.addWidget(process_label)
        layout.addWidget(self.process_input)
        layout.addWidget(self.generate_button)
        layout.addWidget(self.status_label)
        layout.addStretch()

        return widget



    def load_process_data(self):
        """加载流程数据"""
        try:
            with open('process_flow.json', 'r', encoding='utf-8') as f:
                data = json.load(f)
                self.process_data = data.get('processes', {})

            print(f"已加载 {len(self.process_data)} 个流程模板")

        except FileNotFoundError:
            QMessageBox.warning(self, "警告", "未找到 process_flow.json 文件")
            self.process_data = {}
        except json.JSONDecodeError:
            QMessageBox.warning(self, "警告", "JSON 文件格式错误")
            self.process_data = {}
        except Exception as e:
            QMessageBox.critical(self, "错误", f"加载流程数据失败: {str(e)}")
            self.process_data = {}

    def generate_process(self):
        """智能生成流程"""
        user_input = self.process_input.text().strip()
        if not user_input:
            QMessageBox.warning(self, "提示", "请输入流程描述")
            return

        self.status_label.setText("🔍 正在分析关键词...")

        # 关键词匹配和流程生成
        matched_process = self.match_process_by_keywords(user_input)

        if matched_process:
            self.current_process = matched_process
            self.current_step_index = 0

            # 更新流程图
            self.flowchart.update_flowchart(self.current_process)

            # 显示第一步
            self.update_ui()

            self.status_label.setText("✅ 流程生成成功")
        else:
            self.status_label.setText("❌ 未找到匹配的流程")
            QMessageBox.information(self, "提示",
                                   f"未找到与 '{user_input}' 匹配的流程。\n\n"
                                   f"支持的关键词包括：\n"
                                   f"• 毫米波雷达、雷达调试、雷达测试\n"
                                   f"• 激光雷达、激光标定、lidar\n"
                                   f"• 摄像头、相机标定、camera\n"
                                   f"• 设备维护、设备保养、维修")

    def match_process_by_keywords(self, user_input):
        """根据关键词匹配流程"""
        user_input_lower = user_input.lower()

        # 定义关键词映射
        keyword_mapping = {
            "毫米波雷达调试与测试操作流程": [
                "毫米波", "雷达", "调试", "测试", "radar", "毫米波雷达"
            ],
            "激光雷达标定流程": [
                "激光雷达", "激光", "lidar", "标定", "激光标定"
            ],
            "摄像头标定流程": [
                "摄像头", "相机", "camera", "标定", "摄像头标定", "相机标定"
            ],
            "设备维护流程": [
                "设备", "维护", "保养", "维修", "检查", "清洁"
            ]
        }

        # 计算匹配分数
        best_match = None
        best_score = 0

        for process_name, keywords in keyword_mapping.items():
            if process_name in self.process_data:
                score = 0
                for keyword in keywords:
                    if keyword in user_input_lower:
                        score += 1

                if score > best_score:
                    best_score = score
                    best_match = process_name

        # 如果有匹配，返回对应的流程数据
        if best_match and best_score > 0:
            print(f"匹配到流程: {best_match} (匹配分数: {best_score})")
            return self.process_data[best_match]

        return None

    def update_ui(self):
        """更新界面显示"""
        if not self.current_process or self.current_step_index >= len(self.current_process):
            # 清空所有显示
            self.skills_text.setText("请选择流程开始操作")
            self.tools_list.clear()
            self.cautions_text.setText("请选择流程开始操作")
            self.confirm_button.setEnabled(False)
            self.link_button.setEnabled(False)
            return

        current_step = self.current_process[self.current_step_index]

        # 更新技能要点（使用描述信息）
        skills_content = f"步骤 {self.current_step_index + 1}: {current_step['title']}\n\n"
        skills_content += f"操作说明:\n{current_step['description']}"
        self.skills_text.setText(skills_content)

        # 更新工具列表
        self.tools_list.clear()
        for i, tool in enumerate(current_step.get('tools', []), 1):
            # 处理新的工具对象格式
            if isinstance(tool, dict):
                tool_name = tool.get('name', '未知工具')
                tool_image = tool.get('image', '')
                self.add_tool_item(i, tool_name, tool_image)
            else:
                # 兼容旧的字符串格式
                self.tools_list.addItem(f"{i}. {tool}")

        # 更新注意事项
        cautions_content = current_step.get('cautions', '')
        if cautions_content:
            # 将注意事项格式化为编号列表
            cautions_lines = cautions_content.split('。')
            formatted_cautions = ""
            for i, line in enumerate(cautions_lines, 1):
                if line.strip():
                    formatted_cautions += f"{i}. {line.strip()}。\n"
            self.cautions_text.setText(formatted_cautions)
        else:
            self.cautions_text.setText("暂无特殊注意事项")

        # 更新按钮状态
        self.prev_button.setEnabled(self.current_step_index > 0)
        self.confirm_button.setEnabled(True)
        self.link_button.setEnabled(bool(current_step.get('link_url')))

        # 高亮当前步骤
        self.flowchart.highlight_current_step(self.current_step_index)

        # 更新进度
        self.update_progress()

    def add_tool_item(self, index, tool_name, tool_image_path):
        """添加带图片的工具项到列表中"""
        from PyQt5.QtWidgets import QListWidgetItem, QWidget, QHBoxLayout, QLabel
        from PyQt5.QtGui import QPixmap
        from PyQt5.QtCore import Qt
        import os

        # 创建自定义widget
        item_widget = QWidget()
        layout = QHBoxLayout(item_widget)
        layout.setContentsMargins(0, 25, 150, 25)
        layout.setSpacing(12)

        # 添加工具图片
        image_label = QLabel()
        image_label.setFixedSize(80, 80)
        image_label.setStyleSheet("""
            QLabel {
                border: 1px solid #e0e0e0;
                border-radius: 14px;
                background-color: #f9f9f9;
            }
        """)

        # 加载图片
        if tool_image_path and os.path.exists(tool_image_path):
            pixmap = QPixmap(tool_image_path)
            if not pixmap.isNull():
                # 缩放图片以适应标签大小
                scaled_pixmap = pixmap.scaled(400, 400, Qt.KeepAspectRatio, Qt.SmoothTransformation)
                image_label.setPixmap(scaled_pixmap)
                image_label.setAlignment(Qt.AlignCenter)
            else:
                # 图片加载失败，显示默认文本
                image_label.setText("图片")
                image_label.setAlignment(Qt.AlignCenter)
                image_label.setStyleSheet(image_label.styleSheet() + "color: #999999; font-size: 60px;")
        else:
            # 没有图片路径或文件不存在，显示默认图标
            image_label.setText("🔧")
            image_label.setAlignment(Qt.AlignCenter)
            image_label.setStyleSheet(image_label.styleSheet() + "font-size: 60px;")

        # 添加工具名称
        name_label = QLabel(f"{index}. {tool_name}")
        name_label.setStyleSheet("""
            QLabel {
                color: #333333;
                font-size: 22px;
                font-family: "Microsoft YaHei";
                border: none;
                background: transparent;
            }
        """)

        # 调整布局：先显示文字，再显示图片
        layout.addWidget(name_label)
        layout.addStretch()
        layout.addWidget(image_label)

        # 创建列表项
        list_item = QListWidgetItem()
        list_item.setSizeHint(item_widget.sizeHint())

        # 添加到列表
        self.tools_list.addItem(list_item)
        self.tools_list.setItemWidget(list_item, item_widget)

    def update_progress(self):
        """更新进度显示"""
        if not self.current_process:
            self.progress_label.setText("0/0")
            return

        total_steps = len(self.current_process)
        completed_steps = self.current_step_index

        self.progress_label.setText(f"{completed_steps}/{total_steps}")

    def play_voice(self):
        """播放当前步骤语音"""
        if not self.current_process or self.current_step_index >= len(self.current_process):
            return

        current_step = self.current_process[self.current_step_index]
        text_to_speak = f"{current_step['title']}。{current_step['description']}"

        # 在新线程中播放语音
        self.voice_thread = VoiceThread(text_to_speak)
        self.voice_thread.start()

    def play_voice_for_step(self, step_index):
        """播放指定步骤的语音"""
        if not self.current_process or step_index >= len(self.current_process):
            return

        step = self.current_process[step_index]
        text_to_speak = f"{step['title']}。{step['description']}"

        # 在新线程中播放语音
        self.voice_thread = VoiceThread(text_to_speak)
        self.voice_thread.start()

    def open_document(self):
        """打开相关文档"""
        if not self.current_process or self.current_step_index >= len(self.current_process):
            return

        current_step = self.current_process[self.current_step_index]
        link_url = current_step.get('link_url')

        if link_url and os.path.exists(link_url):
            try:
                # 在Windows上使用默认程序打开文件
                os.startfile(link_url)
            except Exception as e:
                QMessageBox.warning(self, "警告", f"无法打开文档: {str(e)}")
        else:
            QMessageBox.information(self, "提示", "文档文件不存在")

    def prev_step(self):
        """上一步"""
        if self.current_step_index > 0:
            self.current_step_index -= 1
            self.update_ui()

    def confirm_step(self):
        """确认当前步骤完成"""
        if not self.current_process:
            return

        # 移动到下一步
        if self.current_step_index < len(self.current_process) - 1:
            self.current_step_index += 1
            self.update_ui()
        else:
            # 流程完成
            QMessageBox.information(self, "完成", "🎉 恭喜！所有流程步骤已完成！")
            # 重置到初始状态
            self.current_process = None
            self.current_step_index = 0
            self.show_welcome_content()

    def show_welcome_content(self):
        """显示欢迎内容"""
        # 技能要点显示系统介绍
        welcome_skills = """
系统功能介绍

• 智能流程生成：输入流程名称自动匹配相关操作步骤
• 可视化引导：流程图直观显示操作进度
• 语音播放：点击喇叭图标播放操作说明
• 文档链接：快速访问相关技术文档

开始使用：请在下方输入您需要的流程名称
    """
        self.skills_text.setText(welcome_skills)
        
        # 所需工具显示支持的流程类型
        welcome_tools = [
            "功能介绍：",
            "这里显示对应流程需要使用的工具名称和图片"
        ]
        self.tools_list.clear()
        for tool in welcome_tools:
            self.tools_list.addItem(tool)
        
        # 注意事项显示使用提示
        welcome_cautions = """
使用提示

1. 在底部输入框中输入流程关键词
2. 点击"智能生成"按钮或按回车键
3. 系统将自动匹配并生成相应流程
4. 按步骤执行，点击"确认完成"进入下一步

支持关键词：雷达、激光、摄像头、标定、调试、维护等
    """
        self.cautions_text.setText(welcome_cautions)

def main():
    app = QApplication(sys.argv)

    # 设置应用程序样式
    app.setStyle('Fusion')

    # 设置全局样式表 - 简洁现代风格
    app.setStyleSheet("""
        QMainWindow {
            background-color: #fafafa;
        }
        QWidget {
            font-family: "Microsoft YaHei", "Segoe UI", sans-serif;
        }
        QGroupBox {
            font-weight: 500;
            font-size: 17px;
            border: 1px solid #e0e0e0;
            border-radius: 12px;
            margin-top: 12px;
            padding-top: 16px;
            background-color: #ffffff;
        }
        QGroupBox::title {
            subcontrol-origin: margin;
            left: 16px;
            padding: 0 8px 0 8px;
            color: #333333;
            background-color: #ffffff;
        }
    """)

    window = ProcessFlowAssistant()
    window.show()

    sys.exit(app.exec())

# 嵌入式流程生成助手组件
class ChatRobotWidget(QWidget):
    """流程生成助手组件 - 直接嵌入到主窗口"""
    def __init__(self, parent=None):
        super().__init__(parent)
        self.parent = parent
        self.current_process = None
        self.current_step_index = 0
        self.process_data = {}

        self._init_ui()
        self.load_process_data()
        self.show_welcome_content()

    def _init_ui(self):
        """初始化UI"""
        try:
            # 主布局 - 垂直布局
            main_layout = QVBoxLayout(self)
            main_layout.setSpacing(10)
            main_layout.setContentsMargins(15, 15, 15, 15)

            # 顶部标题区域
            title_label = QLabel("流程生成助手")
            title_label.setAlignment(Qt.AlignCenter)
            title_label.setFont(QFont("Microsoft YaHei", 18, QFont.Bold))
            title_label.setStyleSheet("""
                QLabel {
                    color: #333333;
                    padding: 10px 0px;
                    background-color: transparent;
                }
            """)
            main_layout.addWidget(title_label)

            # 流程图区域
            self.flowchart = FlowchartWidget()
            # 连接喇叭点击信号
            self.flowchart.voice_clicked.connect(self.play_voice_for_step)
            main_layout.addWidget(self.flowchart)

            # 输入区域
            input_frame = QFrame()
            input_frame.setFrameStyle(QFrame.StyledPanel)
            input_frame.setStyleSheet("""
                QFrame {
                    background-color: #f8f9fa;
                    border: 1px solid #dee2e6;
                    border-radius: 8px;
                    padding: 10px;
                }
            """)
            input_layout = QVBoxLayout(input_frame)

            # 输入标签
            input_label = QLabel("请输入您需要的流程描述：")
            input_label.setFont(QFont("Microsoft YaHei", 12))
            input_label.setStyleSheet("color: #495057; margin-bottom: 5px;")
            input_layout.addWidget(input_label)

            # 输入框和按钮的水平布局
            input_row_layout = QHBoxLayout()

            self.process_input = QLineEdit()
            self.process_input.setPlaceholderText("例如：毫米波雷达调试、激光雷达标定、摄像头校准等...")
            self.process_input.setStyleSheet("""
                QLineEdit {
                    padding: 8px 12px;
                    border: 1px solid #ced4da;
                    border-radius: 4px;
                    font-size: 14px;
                    background-color: white;
                }
                QLineEdit:focus {
                    border-color: #007bff;
                    outline: none;
                }
            """)
            self.process_input.returnPressed.connect(self.generate_process)
            input_row_layout.addWidget(self.process_input)

            # 生成按钮
            self.generate_btn = QPushButton("生成流程")
            self.generate_btn.setFixedSize(100, 36)
            self.generate_btn.setStyleSheet("""
                QPushButton {
                    background-color: #007bff;
                    color: white;
                    border: none;
                    border-radius: 4px;
                    font-size: 14px;
                    font-weight: bold;
                }
                QPushButton:hover {
                    background-color: #0056b3;
                }
                QPushButton:pressed {
                    background-color: #004085;
                }
            """)
            self.generate_btn.clicked.connect(self.generate_process)
            input_row_layout.addWidget(self.generate_btn)

            input_layout.addLayout(input_row_layout)
            main_layout.addWidget(input_frame)

            # 控制区域
            control_frame = QFrame()
            control_frame.setFrameStyle(QFrame.StyledPanel)
            control_frame.setStyleSheet("""
                QFrame {
                    background-color: #f8f9fa;
                    border: 1px solid #dee2e6;
                    border-radius: 8px;
                    padding: 10px;
                }
            """)
            control_layout = QHBoxLayout(control_frame)

            # 控制按钮
            self.prev_btn = QPushButton("⬅ 上一步")
            self.prev_btn.setEnabled(False)
            self.prev_btn.clicked.connect(self.previous_step)

            self.next_btn = QPushButton("下一步 ➡")
            self.next_btn.setEnabled(False)
            self.next_btn.clicked.connect(self.next_step)

            self.reset_btn = QPushButton("🔄 重置")
            self.reset_btn.clicked.connect(self.reset_process)

            # 按钮样式
            button_style = """
                QPushButton {
                    background-color: #6c757d;
                    color: white;
                    border: none;
                    border-radius: 4px;
                    padding: 8px 16px;
                    font-size: 14px;
                    font-weight: bold;
                }
                QPushButton:hover {
                    background-color: #545b62;
                }
                QPushButton:pressed {
                    background-color: #3d4142;
                }
                QPushButton:disabled {
                    background-color: #e9ecef;
                    color: #6c757d;
                }
            """

            self.prev_btn.setStyleSheet(button_style)
            self.next_btn.setStyleSheet(button_style)
            self.reset_btn.setStyleSheet(button_style)

            control_layout.addWidget(self.prev_btn)
            control_layout.addStretch()
            control_layout.addWidget(self.reset_btn)
            control_layout.addStretch()
            control_layout.addWidget(self.next_btn)

            main_layout.addWidget(control_frame)

            # 详细信息区域
            self.detail_area = QScrollArea()
            self.detail_area.setWidgetResizable(True)
            self.detail_area.setStyleSheet("""
                QScrollArea {
                    border: 1px solid #dee2e6;
                    border-radius: 8px;
                    background-color: white;
                }
            """)

            self.detail_widget = QWidget()
            self.detail_layout = QVBoxLayout(self.detail_widget)
            self.detail_layout.setContentsMargins(15, 15, 15, 15)

            self.detail_area.setWidget(self.detail_widget)
            main_layout.addWidget(self.detail_area)

            # 状态栏
            self.status_label = QLabel("💡 请输入流程描述来开始")
            self.status_label.setStyleSheet("""
                QLabel {
                    color: #495057;
                    font-size: 14px;
                    padding: 8px;
                    background-color: #e9ecef;
                    border-radius: 4px;
                }
            """)
            main_layout.addWidget(self.status_label)

        except Exception as e:
            print(f"创建ChatRobotWidget UI失败: {e}")

    def load_process_data(self):
        """加载流程数据"""
        try:
            with open('process_flow.json', 'r', encoding='utf-8') as f:
                data = json.load(f)
                self.process_data = data.get('processes', {})
        except FileNotFoundError:
            # 如果文件不存在，创建默认数据
            self.create_default_process_data()
        except json.JSONDecodeError:
            QMessageBox.critical(self, "错误", "流程数据文件格式错误")
            self.process_data = {}
        except Exception as e:
            QMessageBox.critical(self, "错误", f"加载流程数据失败: {str(e)}")
            self.process_data = {}

    def create_default_process_data(self):
        """创建默认流程数据"""
        default_data = {
            "processes": {
                "毫米波雷达调试": [
                    "连接雷达设备到测试平台",
                    "启动雷达调试软件",
                    "配置雷达参数",
                    "进行距离测试",
                    "进行速度测试",
                    "进行角度测试",
                    "保存测试结果"
                ],
                "激光雷达标定": [
                    "安装激光雷达到标定台",
                    "启动标定软件",
                    "设置标定参数",
                    "进行点云采集",
                    "执行标定算法",
                    "验证标定结果",
                    "保存标定文件"
                ],
                "摄像头校准": [
                    "安装摄像头到校准架",
                    "启动相机校准软件",
                    "拍摄标定板图像",
                    "检测标定板角点",
                    "计算相机内参",
                    "计算畸变参数",
                    "保存校准结果"
                ]
            }
        }

        try:
            with open('process_flow.json', 'w', encoding='utf-8') as f:
                json.dump(default_data, f, ensure_ascii=False, indent=2)
            self.process_data = default_data['processes']
        except Exception as e:
            print(f"创建默认流程数据失败: {e}")
            self.process_data = default_data['processes']

    def generate_process(self):
        """智能生成流程"""
        user_input = self.process_input.text().strip()
        if not user_input:
            QMessageBox.warning(self, "提示", "请输入流程描述")
            return

        self.status_label.setText("🔍 正在分析关键词...")

        # 关键词匹配和流程生成
        matched_process = self.match_process_by_keywords(user_input)

        if matched_process:
            self.current_process = matched_process
            self.current_step_index = 0

            # 更新流程图
            self.flowchart.update_flowchart(self.current_process)

            # 显示第一步
            self.update_ui()

            self.status_label.setText("✅ 流程生成成功")
        else:
            self.status_label.setText("❌ 未找到匹配的流程")
            QMessageBox.information(self, "提示",
                                   f"未找到与 '{user_input}' 匹配的流程。\n\n"
                                   f"支持的关键词包括：\n"
                                   f"• 毫米波雷达、雷达调试、雷达测试\n"
                                   f"• 激光雷达、激光标定、lidar\n"
                                   f"• 摄像头、相机标定、camera\n"
                                   f"• 设备维护、设备保养、维修")

    def match_process_by_keywords(self, user_input):
        """根据关键词匹配流程"""
        user_input_lower = user_input.lower()

        # 关键词映射
        keyword_mapping = {
            "毫米波雷达调试": ["毫米波", "雷达", "radar", "调试", "测试"],
            "激光雷达标定": ["激光", "lidar", "标定", "校准", "点云"],
            "摄像头校准": ["摄像头", "相机", "camera", "校准", "标定", "图像"]
        }

        # 计算匹配分数
        best_match = None
        best_score = 0

        for process_name, keywords in keyword_mapping.items():
            score = 0
            for keyword in keywords:
                if keyword in user_input_lower:
                    score += 1

            if score > best_score and process_name in self.process_data:
                best_score = score
                best_match = process_name

        return self.process_data.get(best_match) if best_match else None

    def show_welcome_content(self):
        """显示欢迎内容"""
        welcome_skills = """
系统功能介绍

🎯 智能流程生成
• 根据关键词自动匹配相应的操作流程
• 支持毫米波雷达、激光雷达、摄像头等设备的调试流程

🔧 支持的设备类型
• 毫米波雷达调试与测试
• 激光雷达标定与校准
• 摄像头校准与图像处理

📋 操作指南
1. 在输入框中描述您需要的流程
2. 点击"生成流程"按钮
3. 系统将自动生成相应的操作步骤
4. 使用控制按钮浏览各个步骤

💡 使用提示
• 输入关键词如"雷达调试"、"激光标定"等
• 支持中英文关键词识别
• 可以使用语音播放功能听取步骤说明
        """

        self.clear_detail_area()

        # 创建欢迎内容标签
        welcome_label = QLabel(welcome_skills)
        welcome_label.setWordWrap(True)
        welcome_label.setStyleSheet("""
            QLabel {
                font-size: 14px;
                line-height: 1.6;
                color: #495057;
                background-color: #f8f9fa;
                padding: 20px;
                border-radius: 8px;
                border: 1px solid #dee2e6;
            }
        """)

        self.detail_layout.addWidget(welcome_label)
        self.detail_layout.addStretch()

    def update_ui(self):
        """更新UI显示"""
        if not self.current_process:
            return

        # 更新按钮状态
        self.prev_btn.setEnabled(self.current_step_index > 0)
        self.next_btn.setEnabled(self.current_step_index < len(self.current_process) - 1)

        # 高亮当前步骤
        self.flowchart.highlight_current_step(self.current_step_index)

        # 显示当前步骤详情
        self.show_step_details()

    def show_step_details(self):
        """显示步骤详情"""
        if not self.current_process or self.current_step_index >= len(self.current_process):
            return

        current_step = self.current_process[self.current_step_index]

        self.clear_detail_area()

        # 步骤标题
        step_title = QLabel(f"步骤 {self.current_step_index + 1}: {current_step}")
        step_title.setFont(QFont("Microsoft YaHei", 16, QFont.Bold))
        step_title.setStyleSheet("""
            QLabel {
                color: #007bff;
                padding: 15px;
                background-color: #e7f3ff;
                border-radius: 8px;
                border-left: 4px solid #007bff;
            }
        """)
        step_title.setWordWrap(True)
        self.detail_layout.addWidget(step_title)

        # 步骤详细说明
        details = self.get_step_details(current_step)
        if details:
            detail_label = QLabel(details)
            detail_label.setWordWrap(True)
            detail_label.setStyleSheet("""
                QLabel {
                    font-size: 14px;
                    line-height: 1.6;
                    color: #495057;
                    padding: 15px;
                    background-color: white;
                    border-radius: 8px;
                    border: 1px solid #dee2e6;
                }
            """)
            self.detail_layout.addWidget(detail_label)

        self.detail_layout.addStretch()

    def get_step_details(self, step_name):
        """获取步骤详细说明"""
        details_mapping = {
            "连接雷达设备到测试平台": "确保雷达设备与测试平台的连接线缆正确连接，检查电源和数据线。",
            "启动雷达调试软件": "打开雷达调试软件，等待软件完全加载并识别设备。",
            "配置雷达参数": "根据测试需求设置雷达的工作频率、功率等参数。",
            "进行距离测试": "使用标准目标物进行距离测量精度测试。",
            "进行速度测试": "使用移动目标测试雷达的速度测量能力。",
            "进行角度测试": "测试雷达的角度分辨率和测量精度。",
            "保存测试结果": "将所有测试数据保存到指定文件夹中。",

            "安装激光雷达到标定台": "将激光雷达固定在标定台上，确保安装牢固。",
            "启动标定软件": "打开激光雷达标定软件，连接设备。",
            "设置标定参数": "配置标定算法的相关参数。",
            "进行点云采集": "采集标定环境的点云数据。",
            "执行标定算法": "运行标定算法计算设备参数。",
            "验证标定结果": "使用测试数据验证标定的准确性。",
            "保存标定文件": "保存标定结果文件供后续使用。",

            "安装摄像头到校准架": "将摄像头固定在校准架上，调整合适的位置。",
            "启动相机校准软件": "打开相机校准软件，连接摄像头设备。",
            "拍摄标定板图像": "使用标定板拍摄多张不同角度的图像。",
            "检测标定板角点": "软件自动检测标定板的角点特征。",
            "计算相机内参": "根据角点信息计算相机的内部参数。",
            "计算畸变参数": "计算镜头的畸变系数。",
            "保存校准结果": "保存相机校准的所有参数文件。"
        }

        return details_mapping.get(step_name, "详细操作说明请参考相关技术文档。")

    def clear_detail_area(self):
        """清空详情区域"""
        while self.detail_layout.count():
            child = self.detail_layout.takeAt(0)
            if child.widget():
                child.widget().deleteLater()

    def previous_step(self):
        """上一步"""
        if self.current_process and self.current_step_index > 0:
            self.current_step_index -= 1
            self.update_ui()
            self.status_label.setText(f"📍 当前步骤: {self.current_step_index + 1}/{len(self.current_process)}")

    def next_step(self):
        """下一步"""
        if self.current_process and self.current_step_index < len(self.current_process) - 1:
            self.current_step_index += 1
            self.update_ui()
            self.status_label.setText(f"📍 当前步骤: {self.current_step_index + 1}/{len(self.current_process)}")

    def reset_process(self):
        """重置流程"""
        self.current_process = None
        self.current_step_index = 0
        self.process_input.clear()
        self.flowchart.steps = []
        self.flowchart.show_welcome = True
        self.flowchart.update()
        self.show_welcome_content()
        self.status_label.setText("💡 请输入流程描述来开始")

        # 重置按钮状态
        self.prev_btn.setEnabled(False)
        self.next_btn.setEnabled(False)

    def play_voice_for_step(self, step_index):
        """播放指定步骤的语音"""
        if self.current_process and 0 <= step_index < len(self.current_process):
            step_text = self.current_process[step_index]
            try:
                # 创建语音线程
                self.voice_thread = VoiceThread(step_text)
                self.voice_thread.start()
                self.status_label.setText(f"🔊 正在播放步骤 {step_index + 1} 的语音...")
            except Exception as e:
                print(f"语音播放失败: {e}")
                self.status_label.setText("❌ 语音播放失败")




def main():
    app = QApplication(sys.argv)

    # 设置应用程序样式
    app.setStyle('Fusion')

    # 设置全局样式表 - 简洁现代风格
    app.setStyleSheet("""
        QMainWindow {
            background-color: #fafafa;
        }
        QWidget {
            font-family: "Microsoft YaHei", "Segoe UI", sans-serif;
        }
        QGroupBox {
            font-weight: 500;
            font-size: 17px;
            border: 1px solid #e0e0e0;
            border-radius: 12px;
            margin-top: 12px;
            padding-top: 16px;
            background-color: #ffffff;
        }
        QGroupBox::title {
            subcontrol-origin: margin;
            left: 16px;
            padding: 0 8px 0 8px;
            color: #333333;
            background-color: #ffffff;
        }
    """)

    window = ProcessFlowAssistant()
    window.show()

    sys.exit(app.exec())

if __name__ == "__main__":
    main()
